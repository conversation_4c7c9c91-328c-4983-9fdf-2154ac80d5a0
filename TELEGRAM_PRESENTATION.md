# How Telegram Alerting Works

## Challenge
Telegram does not allow bots to send messages directly to phone numbers without prior user interaction.

## Vision Guard Solution
To connect users with emergency alerts, Vision Guard uses the following integration flow:

### Integration Process:

1. **User Registration**
   User registers with their phone number on the Vision Guard website.

2. **Manual Bot Activation**
   The system automatically sends the `/start` message to the Telegram bot on behalf of the user.

3. **Chat ID Capture**
   The bot captures the user's Telegram Chat ID when they send `/start`.

4. **Account Linking**
   This Chat ID is securely linked to the user's account for all future emergency communications.

### Key Benefits:

- **Zero Setup Friction** - No manual bot configuration required
- **Instant Connectivity** - Immediate alert capability upon registration  
- **Secure Integration** - Chat IDs are encrypted and securely stored
- **Reliable Delivery** - Direct emergency alerts with video evidence, location data, and AI analysis

This integration allows secure and immediate alert delivery without user setup friction.

---

**Result:** Users receive instant violence detection alerts with video evidence, location data, timestamps, and AI-generated security reports - all automatically configured during the simple registration process.

## Technical Implementation

### Bot Information:
- **Bot Name:** Vision Guard Security
- **Username:** @Visionguard_security_bot
- **Bot Token:** **********:AAFM1pIxo1BzacqrhWAKsS4rSLH8iY13xtI

### Alert Components:
- 📹 **Video Evidence** - Actual footage of detected violence
- 📍 **Location Data** - Camera location and timestamp
- 🤖 **AI Analysis** - Ollama-generated security report
- 🚨 **Emergency Format** - Professional alert messaging

### User Experience:
1. Register → Automatic Telegram connection
2. Start monitoring → Real-time violence detection
3. Violence detected → Instant comprehensive alert
4. No manual setup required
