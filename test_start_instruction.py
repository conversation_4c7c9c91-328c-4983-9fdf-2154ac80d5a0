#!/usr/bin/env python3
"""
Test script to verify the /start instruction message functionality
"""

import requests
import json

# Configuration
BACKEND_URL = "http://localhost:5001"

def test_registration_with_start_instruction():
    """Test user registration and /start instruction message"""
    print("🧪 Testing registration with /start instruction...")
    
    test_user = {
        "fullName": "Test User",
        "email": "<EMAIL>", 
        "phone": "01234567890",
        "password": "testpass123"
    }
    
    try:
        print(f"📝 Registering user: {test_user['fullName']}")
        response = requests.post(f"{BACKEND_URL}/register_user", json=test_user)
        data = response.json()
        
        if response.status_code == 200:
            print("✅ Registration successful!")
            print(f"   Status: {data.get('status')}")
            print(f"   Message: {data.get('message')}")
            print(f"   Telegram Connected: {data.get('telegram_connected')}")
            print(f"   Start Instruction Sent: {data.get('start_instruction_sent')}")
            print(f"   Bot Link: {data.get('telegram_bot_link')}")
            print(f"   Instructions: {data.get('instructions')}")
            
            if data.get('start_instruction_sent'):
                print("\n🎉 SUCCESS: /start instruction message was sent!")
                print("📱 User should check their Telegram for the activation message")
            else:
                print("\n⚠️  No /start instruction sent - user may need to start bot first")
                
            return True
        else:
            print(f"❌ Registration failed: {data.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Registration error: {str(e)}")
        return False

def test_telegram_message_sending():
    """Test sending a direct Telegram message"""
    print("\n🧪 Testing direct Telegram message sending...")
    
    try:
        test_message = {
            "message": "🛡️ **TEST MESSAGE** 🛡️\n\nThis is a test of the /start instruction system!\n\nPlease type /start to activate your alerts."
        }
        
        response = requests.post(f"{BACKEND_URL}/test_telegram", json=test_message)
        data = response.json()
        
        if response.status_code == 200:
            print("✅ Test message sent successfully!")
            print(f"   Response: {data.get('message')}")
            return True
        else:
            print(f"❌ Test message failed: {data.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Test message error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting /start instruction functionality test...\n")
    
    # Test 1: Registration with /start instruction
    registration_success = test_registration_with_start_instruction()
    
    # Test 2: Direct message sending
    message_success = test_telegram_message_sending()
    
    print("\n" + "="*60)
    print("📊 TEST RESULTS:")
    print(f"   Registration with /start instruction: {'✅ PASS' if registration_success else '❌ FAIL'}")
    print(f"   Direct Telegram messaging: {'✅ PASS' if message_success else '❌ FAIL'}")
    
    if registration_success and message_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Users will now receive /start instructions during registration")
    else:
        print("\n⚠️  Some tests failed - check the backend logs")
    
    print("\n🔗 Next steps:")
    print("1. Register a new user on the website")
    print("2. Check Telegram for the activation message")
    print("3. Type /start in the Vision Guard bot")
    print("4. Verify emergency alerts are working")

if __name__ == "__main__":
    main()
