// Auth functions
function checkAuth() {
    const user = JSON.parse(localStorage.getItem('currentUser'));
    const currentPage = window.location.pathname.split('/').pop();

    // If user is not logged in
    if (!user) {
        // Allow access only to register.html and login.html
        if (currentPage !== 'register.html' && currentPage !== 'login.html') {
            window.location.href = 'login.html';
            return null;
        }
    } else {
        // User is logged in
        if (currentPage === 'register.html' || currentPage === 'login.html') {
            // Redirect to index.html if trying to access login or register pages while logged in
            window.location.href = 'index.html';
            return user;
        }
    }

    return user;
}

// Call checkAuth on every page load
document.addEventListener('DOMContentLoaded', checkAuth);

// Handle Register Form
const registerForm = document.querySelector('.register-form');
if (registerForm) {
    registerForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const fullName = document.getElementById('fullName').value;
        const email = document.getElementById('email').value;
        const phone = document.getElementById('phone').value;
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        // Validate phone number
        const phoneRegex = /^[0-9]{11}$/;
        if (!phoneRegex.test(phone)) {
            showAlert('Please enter a valid phone number (11 digits)', 'error');
            return;
        }

        if (password !== confirmPassword) {
            showAlert('Passwords do not match!', 'error');
            return;
        }

        // Register user with backend and send welcome Telegram message
        registerUserWithBackend(fullName, email, phone, password);
    });
}

// Handle Login Form
const loginForm = document.querySelector('.login-form');
if (loginForm) {
    loginForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        const users = JSON.parse(localStorage.getItem('users') || '[]');
        const user = users.find(u => u.email === email && u.password === password);

        if (user) {
            localStorage.setItem('currentUser', JSON.stringify({
                fullName: user.fullName,
                email: user.email,
                phone: user.phone  // Store phone number for Telegram notifications
            }));

            // Set user session in backend for Telegram notifications
            setUserSessionInBackend(user);

            showAlert('Login successful! Redirecting to home page...', 'success');
            setTimeout(() => window.location.href = 'index.html', 1500);
        } else {
            showAlert('Invalid credentials!', 'error');
        }
    });
}

// Function to register user with backend and send welcome Telegram message
async function registerUserWithBackend(fullName, email, phone, password) {
    try {
        // Show loading message
        showAlert('Registering user and sending welcome message...', 'loading');

        // Check local storage for existing users first
        const users = JSON.parse(localStorage.getItem('users') || '[]');

        if (users.some(user => user.email === email)) {
            showAlert('Email already registered!', 'error');
            return;
        }

        if (users.some(user => user.phone === phone)) {
            showAlert('Phone number already registered!', 'error');
            return;
        }

        // Register with backend
        const response = await fetch('http://localhost:5001/register_user', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                fullName: fullName,
                email: email,
                phone: phone,
                password: password
            })
        });

        const data = await response.json();

        if (response.ok) {
            // Save to local storage
            const newUser = { fullName, email, phone, password };
            users.push(newUser);
            localStorage.setItem('users', JSON.stringify(users));

            // Show success message with /start instruction
            if (data.start_instruction_sent) {
                showAlert('Registration successful! Check your Telegram and type /start to activate alerts.', 'success');
                setTimeout(() => {
                    showTelegramActivationModal(phone, fullName, data.telegram_bot_link);
                }, 1500);
            } else {
                // Show Telegram connection modal if no instruction was sent
                showTelegramConnectionModal(phone, fullName);
            }
        } else {
            showAlert(data.message || 'Registration failed', 'error');
        }
    } catch (error) {
        console.error('Error registering user:', error);
        showAlert('Registration failed. Please try again.', 'error');
    }
}

// Function to show Telegram activation modal (when instruction message was sent)
function showTelegramActivationModal(phone, fullName, botLink) {
    const modalHTML = `
        <div id="telegramActivationModal" class="telegram-modal">
            <div class="telegram-modal-content">
                <div class="telegram-header">
                    <h2>🛡️ Registration Successful!</h2>
                    <p>Check your Telegram for activation instructions</p>
                </div>

                <div class="activation-notice">
                    <div class="notice-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="notice-content">
                        <h3>📱 We sent you an activation message!</h3>
                        <p>Check your Telegram and type <strong>/start</strong> to activate emergency alerts.</p>
                    </div>
                </div>

                <div class="connection-steps">
                    <div class="step-item">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>Check your Telegram messages</h3>
                            <p>Look for the Vision Guard activation message</p>
                        </div>
                    </div>

                    <div class="step-item">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>Type <code>/start</code> in the bot</h3>
                            <p>This activates your emergency alert system</p>
                        </div>
                    </div>

                    <div class="step-item">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3>Receive instant alerts!</h3>
                            <p>Get notified immediately when violence is detected</p>
                        </div>
                    </div>
                </div>

                <div class="telegram-actions">
                    <a href="${botLink || 'https://t.me/Visionguard_security_bot'}" target="_blank" class="telegram-btn primary">
                        <i class="fab fa-telegram"></i>
                        Open Vision Guard Bot
                    </a>

                    <button onclick="testTelegramConnection('${phone}')" class="connect-btn">
                        <i class="fas fa-check"></i>
                        Test Connection
                    </button>
                </div>

                <div class="telegram-footer">
                    <button onclick="closeTelegramModal(); window.location.href='login.html'" class="continue-btn">Continue to Login</button>
                    <p class="note">You can test your connection anytime from the dashboard</p>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

// Function to show Telegram connection modal
function showTelegramConnectionModal(phone, fullName) {
    const modalHTML = `
        <div id="telegramConnectionModal" class="telegram-modal">
            <div class="telegram-modal-content">
                <div class="telegram-header">
                    <h2>🛡️ Registration Successful!</h2>
                    <p>Connect Telegram for instant violence alerts</p>
                    <div class="important-note">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Important:</strong> You must type <code>/start</code> in the bot to connect
                    </div>
                </div>

                <div class="connection-steps">
                    <div class="step-item">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>Start the Vision Guard Bot</h3>
                            <p>Click the button below to open Telegram</p>
                        </div>
                    </div>

                    <div class="step-item">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>Type <code>/start</code> in Telegram</h3>
                            <p>This connects your phone number automatically</p>
                        </div>
                    </div>

                    <div class="step-item">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3>Click "Connect" below</h3>
                            <p>We'll link your account after you type /start</p>
                        </div>
                    </div>
                </div>

                <div class="telegram-actions">
                    <a href="https://t.me/Visionguard_security_bot" target="_blank" class="telegram-btn primary">
                        <i class="fab fa-telegram"></i>
                        Open Vision Guard Bot
                    </a>

                    <button onclick="connectTelegram('${phone}')" class="connect-btn">
                        <i class="fas fa-link"></i>
                        Connect Telegram
                    </button>
                </div>

                <div class="telegram-footer">
                    <button onclick="skipTelegramConnection()" class="skip-btn">Skip for now</button>
                    <p class="note">You can connect Telegram later from your dashboard</p>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    addTelegramModalStyles();
}

// Function to connect Telegram automatically
async function connectTelegram(phone) {
    const status = document.querySelector('.telegram-actions');

    try {
        status.innerHTML = '<div class="loading">🔄 Connecting to Telegram...</div>';

        const response = await fetch('http://localhost:5001/connect_telegram', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phone_number: phone
            })
        });

        const data = await response.json();

        if (response.ok) {
            status.innerHTML = '<div class="success">✅ Telegram connected successfully!</div>';
            setTimeout(() => {
                closeTelegramModal();
                showAlert('Registration complete! You will receive violence detection alerts on Telegram.', 'success');
                setTimeout(() => window.location.href = 'login.html', 2000);
            }, 2000);
        } else {
            status.innerHTML = `
                <div class="error">❌ ${data.message}</div>
                <p class="instruction">Please type <strong>/start</strong> in the bot first</p>
                <a href="${data.bot_link || 'https://t.me/Visionguard_security_bot'}" target="_blank" class="telegram-btn primary">
                    <i class="fab fa-telegram"></i>
                    Open Bot & Type /start
                </a>
                <button onclick="connectTelegram('${phone}')" class="connect-btn">
                    <i class="fas fa-link"></i>
                    Try Again
                </button>
            `;
        }

    } catch (error) {
        status.innerHTML = `
            <div class="error">❌ Connection failed. Please try again.</div>
            <p class="instruction">Make sure you typed <strong>/start</strong> in the bot first</p>
            <button onclick="connectTelegram('${phone}')" class="connect-btn">
                <i class="fas fa-link"></i>
                Retry Connection
            </button>
        `;
    }
}

// Function to test Telegram connection
async function testTelegramConnection(phone) {
    const status = document.getElementById('telegram-status') || createStatusDiv();

    try {
        status.innerHTML = '<div class="loading">🔄 Testing Telegram connection...</div>';

        const response = await fetch('http://localhost:5001/test_telegram', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: '🛡️ **TELEGRAM CONNECTION TEST** 🛡️\n\n✅ This is a test message from Vision Guard!\n\nIf you received this, your Telegram is connected successfully!'
            })
        });

        const data = await response.json();

        if (response.ok) {
            status.innerHTML = '<div class="success">✅ Test message sent! Check your Telegram.</div>';
            setTimeout(() => {
                status.innerHTML = '<div class="success">✅ Connection successful! You will receive violence alerts.</div>';
            }, 3000);
        } else {
            status.innerHTML = `
                <div class="error">❌ ${data.message}</div>
                <p class="note">Please make sure you typed <strong>/start</strong> in the bot first.</p>
                <a href="https://t.me/Visionguard_security_bot" target="_blank" class="telegram-btn primary">
                    <i class="fab fa-telegram"></i>
                    Open Bot & Type /start
                </a>
            `;
        }

    } catch (error) {
        status.innerHTML = `
            <div class="error">❌ Connection test failed. Please try again.</div>
            <button onclick="testTelegramConnection('${phone}')" class="connect-btn">
                <i class="fas fa-check"></i>
                Test Again
            </button>
        `;
    }
}

// Helper function to create status div if it doesn't exist
function createStatusDiv() {
    let statusDiv = document.getElementById('telegram-status');
    if (!statusDiv) {
        statusDiv = document.createElement('div');
        statusDiv.id = 'telegram-status';
        const actionsDiv = document.querySelector('.telegram-actions');
        if (actionsDiv) {
            actionsDiv.parentNode.insertBefore(statusDiv, actionsDiv.nextSibling);
        }
    }
    return statusDiv;
}

// Function to skip Telegram connection
function skipTelegramConnection() {
    closeTelegramModal();
    showAlert('Registration successful! You can connect Telegram later for violence alerts.', 'success');
    setTimeout(() => window.location.href = 'login.html', 2000);
}

// Function to close Telegram modal
function closeTelegramModal() {
    const connectionModal = document.getElementById('telegramConnectionModal');
    const activationModal = document.getElementById('telegramActivationModal');
    const styles = document.getElementById('telegramModalStyles');

    if (connectionModal) connectionModal.remove();
    if (activationModal) activationModal.remove();
    if (styles) styles.remove();
}

// Function to add modal styles
function addTelegramModalStyles() {
    const styles = `
        <style id="telegramModalStyles">
            .telegram-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            }

            .telegram-modal-content {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 20px;
                padding: 30px;
                max-width: 500px;
                width: 90%;
                max-height: 90vh;
                overflow-y: auto;
                color: white;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                animation: slideUp 0.3s ease;
            }

            .telegram-header {
                text-align: center;
                margin-bottom: 30px;
            }

            .telegram-header h2 {
                margin: 0 0 10px 0;
                font-size: 24px;
                font-weight: 700;
            }

            .telegram-header p {
                margin: 0;
                opacity: 0.9;
                font-size: 16px;
            }

            .important-note {
                background: rgba(255, 193, 7, 0.2);
                border: 1px solid #ffc107;
                color: #856404;
                padding: 12px 15px;
                border-radius: 8px;
                margin-top: 15px;
                display: flex;
                align-items: center;
                font-size: 14px;
            }

            .important-note i {
                margin-right: 8px;
                color: #ffc107;
            }

            .important-note code {
                background: rgba(255, 255, 255, 0.8);
                padding: 2px 6px;
                border-radius: 4px;
                font-weight: bold;
                color: #495057;
            }

            .instruction {
                margin: 10px 0;
                padding: 8px 12px;
                background: rgba(255, 193, 7, 0.1);
                border-radius: 6px;
                font-size: 14px;
                color: #856404;
            }

            .activation-notice {
                background: linear-gradient(135deg, #4CAF50, #45a049);
                color: white;
                padding: 20px;
                border-radius: 15px;
                margin-bottom: 25px;
                display: flex;
                align-items: center;
                box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
            }

            .notice-icon {
                font-size: 24px;
                margin-right: 15px;
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.1); }
                100% { transform: scale(1); }
            }

            .notice-content h3 {
                margin: 0 0 8px 0;
                font-size: 18px;
                font-weight: 600;
            }

            .notice-content p {
                margin: 0;
                font-size: 14px;
                opacity: 0.9;
            }

            .notice-content code {
                background: rgba(255, 255, 255, 0.2);
                padding: 2px 6px;
                border-radius: 4px;
                font-weight: bold;
            }

            .connection-steps {
                margin-bottom: 30px;
            }

            .step-item {
                display: flex;
                align-items: flex-start;
                margin-bottom: 20px;
                padding: 15px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                backdrop-filter: blur(10px);
            }

            .step-number {
                background: #4CAF50;
                color: white;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                margin-right: 15px;
                flex-shrink: 0;
            }

            .step-content h3 {
                margin: 0 0 5px 0;
                font-size: 16px;
                font-weight: 600;
            }

            .step-content p {
                margin: 0;
                opacity: 0.8;
                font-size: 14px;
            }

            .telegram-actions {
                text-align: center;
                margin-bottom: 30px;
            }

            .telegram-btn, .connect-btn {
                display: inline-block;
                padding: 15px 30px;
                border-radius: 50px;
                text-decoration: none;
                font-weight: 600;
                font-size: 16px;
                transition: all 0.3s ease;
                margin: 10px;
                border: none;
                cursor: pointer;
            }

            .telegram-btn {
                background: #0088cc;
                color: white;
            }

            .connect-btn {
                background: #4CAF50;
                color: white;
            }

            .telegram-btn:hover, .connect-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            }

            .telegram-btn i, .connect-btn i {
                margin-right: 10px;
                font-size: 18px;
            }

            .telegram-footer {
                text-align: center;
            }

            .skip-btn {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                padding: 12px 20px;
                border: none;
                border-radius: 25px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .skip-btn:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            .note {
                margin-top: 10px;
                opacity: 0.7;
                font-size: 12px;
            }

            .loading, .success, .error {
                padding: 15px;
                border-radius: 10px;
                margin: 10px 0;
                font-weight: 600;
            }

            .loading {
                background: rgba(255, 193, 7, 0.2);
                color: #ffc107;
            }

            .success {
                background: rgba(76, 175, 80, 0.2);
                color: #4caf50;
            }

            .error {
                background: rgba(244, 67, 54, 0.2);
                color: #f44336;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes slideUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            @media (max-width: 600px) {
                .telegram-modal-content {
                    padding: 20px;
                    margin: 20px;
                }
            }
        </style>
    `;

    document.head.insertAdjacentHTML('beforeend', styles);
}

// All Telegram modal functions removed - using automatic connection now

// Function to set user session in backend for Telegram notifications
async function setUserSessionInBackend(user) {
    try {
        const response = await fetch('http://localhost:5001/set_user_session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phone_number: user.phone,
                full_name: user.fullName,
                email: user.email
            })
        });

        const data = await response.json();
        if (response.ok) {
            console.log('User session set in backend for Telegram notifications');
        } else {
            console.error('Failed to set user session in backend:', data.message);
        }
    } catch (error) {
        console.error('Error setting user session in backend:', error);
    }
}

// Logout functionality
const logoutBtn = document.getElementById('logoutBtn');
if (logoutBtn) {
    logoutBtn.addEventListener('click', (e) => {
        e.preventDefault();
        localStorage.removeItem('currentUser');
        // Create and show alert
        const alert = document.createElement('div');
        alert.className = 'alert alert-success fade-in show';
        alert.textContent = 'Logged out successfully!';
        document.body.appendChild(alert);

        // Redirect after delay
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 1000);
    });
}

// Enhanced Alert function with better styling
function showAlert(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} fade-in`;
    alert.textContent = message;

    const form = document.querySelector('form');
    form.parentNode.insertBefore(alert, form);

    // Add fade-in animation
    setTimeout(() => alert.classList.add('show'), 100);

    // Remove alert after delay
    setTimeout(() => {
        alert.classList.remove('show');
        setTimeout(() => alert.remove(), 300);
    }, 3000);
}

// Password visibility toggle
document.addEventListener('DOMContentLoaded', function () {
    const passwordToggles = document.querySelectorAll('.password-toggle');

    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', function (e) {
            e.preventDefault();

            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');

            // Toggle password visibility
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
                this.classList.add('showing');

                // Auto-hide after 3 seconds
                setTimeout(() => {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                    this.classList.remove('showing');
                }, 3000);
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
                this.classList.remove('showing');
            }

            // Add ripple effect
            const ripple = document.createElement('div');
            ripple.classList.add('ripple');
            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 1000);
        });
    });
});

