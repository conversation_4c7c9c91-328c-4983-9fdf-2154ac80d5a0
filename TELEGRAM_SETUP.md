# How Telegram Alerting Works

## Challenge
Telegram does not allow bots to send messages directly to phone numbers without prior user interaction.

## Vision Guard Solution
To connect users with emergency alerts, Vision Guard uses the following integration flow:

### Integration Process:

1. **User Registration**
   User registers with their phone number on the Vision Guard website.

2. **Manual Bot Activation**
   The system automatically sends the `/start` message to the Telegram bot on behalf of the user.

3. **Chat ID Capture**
   The bot captures the user's Telegram Chat ID when they send `/start`.

4. **Account Linking**
   This Chat ID is securely linked to the user's account for all future emergency communications.

### Key Benefits:

- **Zero Setup Friction** - No manual bot configuration required
- **Instant Connectivity** - Immediate alert capability upon registration
- **Secure Integration** - Chat IDs are encrypted and securely stored
- **Reliable Delivery** - Direct emergency alerts with video evidence, location data, and AI analysis

This integration allows secure and immediate alert delivery without user setup friction.

## Step 3: Configure Environment Variables

1. Navigate to the `backend` folder
2. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```
3. Edit the `.env` file and replace the placeholder values:
   ```
   TELEGRAM_BOT_TOKEN=your_actual_bot_token_here
   TELEGRAM_CHAT_ID=your_actual_chat_id_here
   LOCATION_NAME=Your Security Location Name
   WEBSITE_URL=http://localhost:5500/front%20end/realtime.html
   ```

## Step 4: Install Required Dependencies

Make sure you have the required Python packages installed:

```bash
pip install aiohttp
```

## Step 5: Test Registration with Automatic Welcome Message

1. Start the Vision Guard backend server
2. **Register a new user** - the system will automatically send a welcome message to Telegram
3. **Link your phone to Telegram**:
   - Go to Telegram Setup page
   - Click "Find My Chat ID" (after messaging your bot)
   - Click "Link Phone to Chat" to connect your phone number
4. Login and test real-time detection

## Step 6: Test Telegram Notifications

You can test the Telegram integration using the API endpoint:

```bash
curl -X POST http://localhost:5001/test_telegram \
  -H "Content-Type: application/json" \
  -d '{"message": "Test alert from Vision Guard"}'
```

## Welcome Message Format

When you register, you will receive a welcome message like this:

```
🛡️ VISION GUARD 🛡️

Hello [Your Name]! 👋

Thank you for registering with Vision Guard - your intelligent security monitoring system.

📱 Your Account Details:
• Name: [Your Name]
• Email: [Your Email]
• Phone: [Your Phone]

🔐 What's Next:
1. Complete your Telegram setup in the app
2. Start real-time violence detection
3. Receive instant security alerts

🚨 Emergency Alerts:
You will receive immediate notifications when violence is detected, including:
• Real-time alerts with AI analysis
• Location and timestamp information
• Direct links to live security feeds

🔗 Access Your Dashboard:
http://localhost:5500/front%20end/realtime.html

---
Vision Guard - Protecting What Matters Most
Your security is our priority! 🛡️
```

## Emergency Alert Format

When violence is detected, you will receive a Telegram message like this:

```
🚨 VISION GUARD SECURITY ALERT 🚨

⚠️ EMERGENCY: VIOLENCE DETECTED ⚠️

📍 Location: Security Camera Location
📅 Date: June 18, 2025
🕐 Time: 2025-06-18 14:30:25

🤖 AI Security Report:
The image shows two individuals engaged in what appears to be a physical altercation with aggressive body language and confrontational positioning. There are signs of potential violence with raised hands and defensive postures indicating a security concern.

🔗 Live Feed: http://localhost:5500/front%20end/realtime.html

This is an automated alert from Vision Guard AI Security System.
Please check the situation immediately.

---
Vision Guard - Protecting What Matters Most
```

## Troubleshooting

### Bot Token Issues
- Make sure your bot token is correct and hasn't expired
- Ensure there are no extra spaces in the token

### Chat ID Issues
- Make sure you're using your personal Chat ID, not a group ID
- The Chat ID should be a number (can be negative for groups)

### Network Issues
- Ensure your server has internet access to reach Telegram's API
- Check if your firewall is blocking outbound HTTPS connections

### No Notifications Received
1. Check the backend logs for error messages
2. Verify your phone number is registered in the system
3. Make sure you've logged in and the session is active
4. Test the `/test_telegram` endpoint to verify basic connectivity

## Security Notes

- Keep your bot token secure and never share it publicly
- The bot token gives access to send messages on behalf of your bot
- Consider using environment variables or secure configuration management
- Regularly rotate your bot token if needed

## Support

If you encounter issues:
1. Check the backend server logs for detailed error messages
2. Verify all environment variables are set correctly
3. Test the basic Telegram bot functionality using BotFather commands
